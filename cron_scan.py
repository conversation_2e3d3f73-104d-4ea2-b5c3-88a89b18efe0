import os
from subprocess import run, CalledProcessError, PIPE
import json
import datetime

def cron_once():
    # 一次性的定时任务

    user_ids = ['2656274875']
    crawl_by_user_ids(user_ids)
    tasks = get_tasks()
    save2db(tasks)
    print(f'Cron job completed. {len(tasks)} tasks added to database.')




    

def crawl_by_user_ids(user_ids):
    original_dir = os.getcwd()

    os.chdir(os.path.join('WeiboSpider', 'weibospider'))
    try:
        # Build command with separate arguments for each user ID
        cmd = ['python', 'run_spider.py', 'tweet_by_user_id', '--tweet_user_ids'] + user_ids
        result = run(cmd, check=True, stderr=PIPE, text=True)
    except CalledProcessError as e:
        print(f"Command failed with return code {e.returncode}")
        print(f"Command: {' '.join(e.cmd)}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if e.stdout:
            print(f"Standard output: {e.stdout}")
        raise  # Re-raise the exception
    finally:
        os.chdir(original_dir)

def get_tasks():
    file_dir = 'WeiboSpider/output/tweet_by_user_id_tweet.jsonl'
    tasks = []
    with open(file_dir, 'r') as f:
        for line in f:
            data = json.loads(line)
            tweet_id = data['mblogid']
            created_at = data['created_at']
            timestamp = int(datetime.datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S').timestamp())
            tasks.append({'tweet_id': tweet_id, 'created_at': timestamp})
    return tasks

def save2db(tasks):
    pass

if __name__ == '__main__':
    cron_once()